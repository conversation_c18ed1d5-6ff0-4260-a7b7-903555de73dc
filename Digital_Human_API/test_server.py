#!/usr/bin/env python3
"""
简化的测试服务器，用于验证基本环境和依赖
"""

from flask import Flask, request, jsonify
from flask_cors import CORS
import json
import os

app = Flask(__name__)
CORS(app)  # 允许跨域请求

@app.route('/test', methods=['GET'])
def test():
    """测试接口"""
    return jsonify({
        "status": "success",
        "message": "Digital Human API is running!",
        "python_version": "3.11.13",
        "environment": "Digital"
    })

@app.route('/Login', methods=['POST'])
def Login():
    """简化的登录接口"""
    try:
        POST_JSON = request.get_json()
        user_name = POST_JSON.get("User", "")
        user_password = POST_JSON.get("Password", "")
        
        # 简单的测试验证
        if user_name and user_password:
            return jsonify(result="Success")
        else:
            return jsonify(result="Failed")
    except Exception as e:
        return jsonify(result="Failed", error=str(e))

@app.route('/health', methods=['GET'])
def health():
    """健康检查接口"""
    try:
        # 检查基本依赖
        import torch
        import numpy
        import cv2
        import PIL
        
        return jsonify({
            "status": "healthy",
            "dependencies": {
                "torch": torch.__version__,
                "numpy": numpy.__version__,
                "opencv": cv2.__version__,
                "pillow": PIL.__version__
            },
            "cuda_available": torch.cuda.is_available(),
            "gpu_count": torch.cuda.device_count() if torch.cuda.is_available() else 0
        })
    except Exception as e:
        return jsonify({
            "status": "error",
            "error": str(e)
        })

if __name__ == '__main__':
    print("Starting Digital Human API Test Server...")
    print("Server will run on http://0.0.0.0:5000")
    print("Test endpoints:")
    print("  GET  /test   - Basic test")
    print("  GET  /health - Health check")
    print("  POST /Login  - Login test")
    
    app.run(host='0.0.0.0', port=5000, debug=True)
