build:
  gpu: true
  cuda: "11.3"
  python_version: "3.8"
  system_packages:
    - "ffmpeg"
    - "libgl1-mesa-glx"
    - "libglib2.0-0"
  python_packages:
    - "torch==1.12.1"
    - "torchvision==0.13.1"
    - "torchaudio==0.12.1"
    - "joblib==1.1.0"
    - "scikit-image==0.19.3"
    - "basicsr==1.4.2"
    - "facexlib==0.3.0"
    - "resampy==0.3.1"
    - "pydub==0.25.1"
    - "scipy==1.10.1"
    - "kornia==0.6.8"
    - "face_alignment==1.3.5"
    - "imageio==2.19.3"
    - "imageio-ffmpeg==0.4.7"
    - "librosa==0.9.2" #
    - "tqdm==4.65.0"
    - "yacs==0.1.8"
    - "gfpgan==1.3.8"
    - "dlib-bin==19.24.1"
    - "av==10.0.0"
    - "trimesh==3.9.20"
  run:
    - mkdir -p /root/.cache/torch/hub/checkpoints/ && wget --output-document "/root/.cache/torch/hub/checkpoints/s3fd-619a316812.pth" "https://www.adrianbulat.com/downloads/python-fan/s3fd-619a316812.pth"
    - mkdir -p /root/.cache/torch/hub/checkpoints/ && wget --output-document "/root/.cache/torch/hub/checkpoints/2DFAN4-cd938726ad.zip" "https://www.adrianbulat.com/downloads/python-fan/2DFAN4-cd938726ad.zip"

predict: "predict.py:Predictor"
